'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { ArrowRight, Package, Zap, Code, ExternalLink } from 'lucide-react'
import { CodeBlock } from '@/components/code-block'

const quickStartCode = `# Install the complete package
npm install designers

# Or install individual packages
npm install designers-core designers-react designers-animations`

const usageCode = `import designers from 'designers'
import { useTheme, ThemeProvider } from 'designers/react'
import { fadeIn, slideUp } from 'designers/animations'

function App() {
  return (
    <ThemeProvider>
      <div className="p-8">
        <h1 className="text-4xl font-bold gradient-text">
          Welcome to Designers
        </h1>
      </div>
    </ThemeProvider>
  )
}`

const cliCode = `# Initialize a new project
npx designers init

# Follow the interactive setup
? What's your project name? my-app
? Which UI library would you like to integrate? shadcn/ui
? Enable animations? Yes
? Setup Tailwind CSS? Yes

✨ Project initialized successfully!`

export default function DocsPage() {
  return (
    <div className="prose-custom">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1>Introduction</h1>
        <p className="text-xl text-muted-foreground">
          Designers is a complete, lightweight, headless design system for React applications. 
          Build beautiful, consistent UIs with automatic Tailwind CSS integration, 
          powerful animations, and seamless UI library support.
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        className="my-8 grid grid-cols-1 md:grid-cols-2 gap-6"
      >
        <Link
          href="/docs/installation"
          className="group block p-6 border border-border rounded-lg hover:border-primary/50 transition-colors no-underline"
        >
          <div className="flex items-center space-x-3 mb-3">
            <Package className="h-6 w-6 text-primary" />
            <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
              Quick Start
            </h3>
          </div>
          <p className="text-muted-foreground mb-3">
            Get up and running in minutes with our simple installation guide.
          </p>
          <div className="flex items-center text-primary text-sm font-medium">
            Get started <ArrowRight className="ml-1 h-4 w-4" />
          </div>
        </Link>

        <Link
          href="/docs/components"
          className="group block p-6 border border-border rounded-lg hover:border-primary/50 transition-colors no-underline"
        >
          <div className="flex items-center space-x-3 mb-3">
            <Code className="h-6 w-6 text-primary" />
            <h3 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors">
              Components
            </h3>
          </div>
          <p className="text-muted-foreground mb-3">
            Explore our comprehensive component library and APIs.
          </p>
          <div className="flex items-center text-primary text-sm font-medium">
            Browse components <ArrowRight className="ml-1 h-4 w-4" />
          </div>
        </Link>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h2>Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mt-0.5">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
              </div>
              <div>
                <h4 className="font-medium text-foreground">Headless Design</h4>
                <p className="text-sm text-muted-foreground">Unstyled, accessible components that you can customize to match your design.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mt-0.5">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
              </div>
              <div>
                <h4 className="font-medium text-foreground">TypeScript First</h4>
                <p className="text-sm text-muted-foreground">Built with TypeScript for excellent developer experience and type safety.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mt-0.5">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
              </div>
              <div>
                <h4 className="font-medium text-foreground">Tailwind Integration</h4>
                <p className="text-sm text-muted-foreground">Automatic Tailwind CSS integration with design tokens and utilities.</p>
              </div>
            </div>
          </div>
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mt-0.5">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
              </div>
              <div>
                <h4 className="font-medium text-foreground">Animation Ready</h4>
                <p className="text-sm text-muted-foreground">Built-in support for Framer Motion and GSAP animations.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mt-0.5">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
              </div>
              <div>
                <h4 className="font-medium text-foreground">UI Library Support</h4>
                <p className="text-sm text-muted-foreground">Seamless integration with shadcn/ui, MUI, Chakra UI, and Mantine.</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mt-0.5">
                <div className="w-2 h-2 bg-green-600 rounded-full"></div>
              </div>
              <div>
                <h4 className="font-medium text-foreground">CLI Tools</h4>
                <p className="text-sm text-muted-foreground">Powerful CLI for project initialization and configuration.</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <h2>Installation</h2>
        <p>Get started with Designers in seconds:</p>
        <CodeBlock code={quickStartCode} language="bash" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <h2>Basic Usage</h2>
        <p>Here's a simple example to get you started:</p>
        <CodeBlock code={usageCode} language="typescript" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <h2>CLI Initialization</h2>
        <p>Use our CLI to set up a new project with all the best practices:</p>
        <CodeBlock code={cliCode} language="bash" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
        className="mt-12 p-6 border border-border rounded-lg bg-muted/30"
      >
        <h3 className="text-lg font-semibold mb-3">What's Next?</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Link
            href="/docs/installation"
            className="flex items-center space-x-2 text-primary hover:underline"
          >
            <Package className="h-4 w-4" />
            <span>Installation Guide</span>
          </Link>
          <Link
            href="/docs/components"
            className="flex items-center space-x-2 text-primary hover:underline"
          >
            <Code className="h-4 w-4" />
            <span>Component Library</span>
          </Link>
          <Link
            href="/docs/examples"
            className="flex items-center space-x-2 text-primary hover:underline"
          >
            <Zap className="h-4 w-4" />
            <span>Examples</span>
          </Link>
          <Link
            href="https://github.com/arkitkarmokar/designers"
            className="flex items-center space-x-2 text-primary hover:underline"
          >
            <ExternalLink className="h-4 w-4" />
            <span>GitHub Repository</span>
          </Link>
        </div>
      </motion.div>
    </div>
  )
}
