'use client'

import { motion } from 'framer-motion'
import { Package, Terminal, CheckCircle, AlertCircle } from 'lucide-react'
import { CodeBlock } from '@/components/code-block'

const installCommands = {
  npm: 'npm install designers',
  yarn: 'yarn add designers',
  pnpm: 'pnpm add designers',
}

const individualPackages = `# Install individual packages
npm install designers-core
npm install designers-react
npm install designers-animations
npm install designers-integrations
npm install designers-tailwind-plugin
npm install designers-cli`

const cliSetup = `# Install CLI globally
npm install -g designers-cli

# Or use npx
npx designers init

# Follow the interactive setup
? What's your project name? my-awesome-app
? Which UI library would you like to integrate? shadcn/ui
? Enable animations? Yes
? Setup Tailwind CSS? Yes

✨ Project initialized successfully!`

const nextSteps = `// 1. Import and use components
import { useTheme, ThemeProvider } from 'designers/react'
import { fadeIn } from 'designers/animations'

// 2. Add Tailwind plugin to your config
// tailwind.config.js
module.exports = {
  plugins: [
    require('designers/tailwind-plugin')
  ]
}

// 3. Start building!
function App() {
  return (
    <ThemeProvider>
      <div className="p-8">
        <h1 className="text-4xl font-bold gradient-text">
          Hello Designers!
        </h1>
      </div>
    </ThemeProvider>
  )
}`

export default function InstallationPage() {
  return (
    <div className="prose-custom">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <h1>Installation</h1>
        <p className="text-xl text-muted-foreground">
          Get started with Designers in your React project. Choose between the complete package 
          or individual components based on your needs.
        </p>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
      >
        <h2>Quick Installation</h2>
        <p>
          The easiest way to get started is to install the complete Designers package, 
          which includes all components, utilities, and integrations:
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 my-6">
          {Object.entries(installCommands).map(([manager, command]) => (
            <div key={manager} className="border border-border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Terminal className="h-4 w-4 text-primary" />
                <span className="font-medium text-sm uppercase tracking-wider text-muted-foreground">
                  {manager}
                </span>
              </div>
              <CodeBlock code={command} language="bash" />
            </div>
          ))}
        </div>

        <div className="flex items-start space-x-3 p-4 border border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20 rounded-lg my-6">
          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-green-800 dark:text-green-200 mb-1">
              Recommended Approach
            </h4>
            <p className="text-sm text-green-700 dark:text-green-300">
              Installing the main package gives you everything you need and ensures all components work together seamlessly.
            </p>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <h2>Individual Packages</h2>
        <p>
          If you prefer more granular control or want to minimize bundle size, 
          you can install individual packages:
        </p>
        <CodeBlock code={individualPackages} language="bash" />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 my-6">
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Package className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium">designers-core</h4>
                <p className="text-sm text-muted-foreground">Design tokens, utilities, and core functionality</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Package className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium">designers-react</h4>
                <p className="text-sm text-muted-foreground">React components, hooks, and providers</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Package className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium">designers-animations</h4>
                <p className="text-sm text-muted-foreground">Framer Motion and GSAP animation presets</p>
              </div>
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <Package className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium">designers-integrations</h4>
                <p className="text-sm text-muted-foreground">UI library integrations (shadcn, MUI, etc.)</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Package className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium">designers-tailwind-plugin</h4>
                <p className="text-sm text-muted-foreground">Tailwind CSS plugin with design tokens</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <Package className="h-5 w-5 text-primary mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium">designers-cli</h4>
                <p className="text-sm text-muted-foreground">Command-line tools for project setup</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        <h2>CLI Setup (Recommended)</h2>
        <p>
          Use our CLI tool to set up a new project with all the best practices and configurations:
        </p>
        <CodeBlock code={cliSetup} language="bash" />

        <div className="flex items-start space-x-3 p-4 border border-blue-200 dark:border-blue-800 bg-blue-50 dark:bg-blue-950/20 rounded-lg my-6">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <div>
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">
              Interactive Setup
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              The CLI will guide you through setting up Tailwind CSS, choosing UI libraries, 
              configuring animations, and more.
            </p>
          </div>
        </div>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <h2>Next Steps</h2>
        <p>
          After installation, you can start using Designers in your project:
        </p>
        <CodeBlock code={nextSteps} language="typescript" />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        className="mt-8 p-6 border border-border rounded-lg bg-muted/30"
      >
        <h3 className="text-lg font-semibold mb-3">Requirements</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span>React 18.0.0 or higher</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span>TypeScript 5.0.0 or higher (recommended)</span>
          </div>
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <span>Tailwind CSS 3.0.0 or higher (optional but recommended)</span>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
